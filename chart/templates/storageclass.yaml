{{- if .Values.storageClass.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ required "StorageClass name is required. Please provide .Values.storageClass.name in respective Values file" .Values.storageClass.name }}
parameters:
  provisioner: {{ required "StorageClass provisioner is required. Please provide .Values.storageClass.provisioner in respective Values file" .Values.storageClass.provisioner }}
provisioner: {{ required "StorageClass provisioner is required. Please provide .Values.storageClass.provisioner in respective Values file" .Values.storageClass.provisioner }}
reclaimPolicy: {{ required "StorageClass reclaimPolicy is required. Please provide .Values.storageClass.reclaimPolicy in respective Values file" .Values.storageClass.reclaimPolicy }}
volumeBindingMode: {{ required "StorageClass volumeBindingMode is required. Please provide .Values.storageClass.volumeBindingMode in respective Values file" .Values.storageClass.volumeBindingMode }}
allowVolumeExpansion: {{ required "StorageClass allowVolumeExpansion is required. Please provide .Values.storageClass.allowVolumeExpansion in respective Values file" .Values.storageClass.allowVolumeExpansion }}
{{- end }}