{{- if .Values.persistentVolumeClaims.repos.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: repos-pvc
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: sca-efs-sc
  resources:
    requests:
      storage: {{ required "Repos PVC storage is required. Please provide .Values.persistentVolumeClaims.repos.storage in values.yaml" .Values.persistentVolumeClaims.repos.storage }}
{{- end }}