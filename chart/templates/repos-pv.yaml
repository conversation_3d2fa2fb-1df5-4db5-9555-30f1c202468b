{{- if .Values.persistentVolumes.repos.volumeHandle }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: repos-pv
spec:
  capacity:
    storage: 4Ti
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: sca-efs-sc
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ .Values.persistentVolumes.repos.volumeHandle }}
    fsType: nfs
{{- end }}