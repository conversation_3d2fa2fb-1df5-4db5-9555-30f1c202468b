{{- if .Values.persistentVolumes.repos.enabled }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: repos-pv
spec:
  capacity:
    storage: {{ required "Repos PV storage capacity is required. Please provide .Values.persistentVolumeClaims.repos.storage in values.yaml" .Values.persistentVolumeClaims.repos.storage }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: sca-efs-sc
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ required "Repos PV volumeHandle is required. Please provide .Values.persistentVolumes.repos.volumeHandle in respective Values file" .Values.persistentVolumes.repos.volumeHandle }}
    fsType: nfs
{{- end }}