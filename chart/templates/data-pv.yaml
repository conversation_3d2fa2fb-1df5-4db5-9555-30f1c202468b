{{- if .Values.persistentVolumes.data.enabled }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: data-pv
spec:
  capacity:
    storage: {{ required "Data PV storage capacity is required. Please provide .Values.persistentVolumeClaims.data.storage in values.yaml" .Values.persistentVolumeClaims.data.storage }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: sca-efs-sc
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ required "Data PV volumeHandle is required. Please provide .Values.persistentVolumes.data.volumeHandle in respective Values file" .Values.persistentVolumes.data.volumeHandle }}
    fsType: nfs
{{- end }}