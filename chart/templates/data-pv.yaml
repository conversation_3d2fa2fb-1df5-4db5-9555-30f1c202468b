{{- if .Values.persistentVolumes.data.volumeHandle }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: data-pv
spec:
  capacity:
    storage: 20Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: sca-efs-sc
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ .Values.persistentVolumes.data.volumeHandle }}
    fsType: nfs
{{- end }}