{{- if .Values.persistentVolumeClaims.data.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: data-pvc
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ required "Data PVC storage is required. Please provide .Values.persistentVolumeClaims.data.storage in values.yaml" .Values.persistentVolumeClaims.data.storage }}
  storageClassName: sca-efs-sc
{{- end }}