---
# Source: ns-essentials/templates/storageclass.yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: sca-efs-sc
parameters:
  provisioner: efs.csi.aws.com
provisioner: efs.csi.aws.com
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
---
# Source: ns-essentials/templates/data-pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: data-pv
spec:
  capacity:
    storage: 20Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: sca-efs-sc
  csi:
    driver: efs.csi.aws.com
    volumeHandle: fs-35e613b5:/
    fsType: nfs
---
# Source: ns-essentials/templates/repos-pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: repos-pv
spec:
  capacity:
    storage: 4Ti
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: sca-efs-sc
  csi:
    driver: efs.csi.aws.com
    volumeHandle: fs-0293eebacf9797569:/daily.0
    fsType: nfs
---
# Source: ns-essentials/templates/data-pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: data-pvc
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi
  storageClassName: sca-efs-sc
---
# Source: ns-essentials/templates/repos-pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: repos-pvc
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: sca-efs-sc
  resources:
    requests:
      storage: 4Ti
